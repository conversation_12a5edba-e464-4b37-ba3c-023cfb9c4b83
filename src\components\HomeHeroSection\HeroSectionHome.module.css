@value variables: "@styles/variables.module.css";
@value gray900, colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-lg, breakpoint-xl-1440, breakpoint-xl-2000 from breakpoints;

.embla {
  overflow: hidden;
  background-color: colorBlack;
}

.embla_placeholder {
  position: relative;
  object-fit: cover;
  background-color: colorBlack;
  width: 100%;
  height: 100vh;
  min-height: 600px;
  max-height: 900px;
  display: flex;
  padding: 149px 124px 80px 124px;
  background-size: cover !important;
  background-repeat: no-repeat !important;
  background-position: center;
  overflow: hidden;

  @media (min-width: breakpoint-xl-2000) {
    padding: 174px 150px 80px 150px;
    max-height: 1000px;
  }

  @media (max-width: breakpoint-xl-1440) {
    height: 90vh;
    min-height: 550px;
    max-height: 800px;
    padding: 120px 100px 60px 100px;
  }

  @media (max-width: breakpoint-lg) {
    height: 85vh;
    min-height: 500px;
    max-height: 700px;
    padding: 100px 80px 50px 80px;
  }

  @media (max-width: breakpoint-md) {
    height: 80vh;
    min-height: 450px;
    max-height: 600px;
    padding: 80px 32px 40px 32px;
  }

  @media (max-width: breakpoint-sm) {
    height: 70vh;
    min-height: 400px;
    max-height: 500px;
    padding: 60px 20px 30px 20px;
  }
}

.inner_container_blur {
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 103px;
  min-height: 512px;

  .section_top {
    opacity: 1;
  }
}

.embla_resources {
  overflow: hidden;
  background-color: gray300;
}

.embla__viewport {
  width: 100%;
  height: 100vh;
  min-height: 600px;
  max-height: 900px;

  @media (min-width: breakpoint-xl-2000) {
    max-height: 1000px;
  }

  @media (max-width: breakpoint-xl-1440) {
    height: 90vh;
    min-height: 550px;
    max-height: 800px;
  }

  @media (max-width: breakpoint-lg) {
    height: 85vh;
    min-height: 500px;
    max-height: 700px;
  }

  @media (max-width: breakpoint-md) {
    height: 80vh;
    min-height: 450px;
    max-height: 600px;
  }

  @media (max-width: breakpoint-sm) {
    height: 70vh;
    min-height: 400px;
    max-height: 500px;
  }
}

.embla__viewport_resources {
  width: 100%;
}

.embla__container {
  display: flex;
}

.embla__slide_image {
  object-fit: cover;
  object-position: center;
  width: 100%;
  height: 100%;
}

.thumbnail_image {
  box-shadow: none !important;
  object-fit: cover;
  object-position: center;
  width: 100%;
  height: auto;
  aspect-ratio: 16/9;

  @media (max-width: breakpoint-sm) {
    aspect-ratio: 4/3;
  }
}

.image_hidden {
  display: none;
  transition: opacity 1.3s ease-in-out;
}

.image_visible {
  display: block;
  transition: opacity 1.3s ease-in-out;
}

.embla__slide {
  flex: 0 0 100%;
  min-width: 0;
  opacity: 0;
  transition: opacity ease-in-out;
  position: relative;

  background-size: cover !important;
  background-repeat: no-repeat !important;
  background-position: center;
  padding: 149px 124px 80px 124px;

  @media (min-width: breakpoint-xl-2000) {
    padding: 174px 150px 80px 150px;
  }

  @media (max-width: breakpoint-xl-1440) {
    padding: 120px 100px 60px 100px;
  }

  @media (max-width: breakpoint-lg) {
    padding: 100px 80px 50px 80px;
  }

  @media (max-width: breakpoint-md) {
    padding: 80px 32px 40px 32px;
  }

  @media (max-width: breakpoint-sm) {
    padding: 60px 20px 30px 20px;
  }
}

.embla__slide_resources {
  flex: 0 0 100%;
  min-width: 0;
  opacity: 0;
  transition: opacity ease-in-out;

  background-size: cover !important;
  background-repeat: no-repeat !important;
  background-position: center;
  padding: 80px 120px;

  @media (min-width: breakpoint-xl-2000) {
    padding: 80px 120px;
  }

  @media (max-width: breakpoint-md) {
    padding-left: 32px;
    padding-right: 32px;
  }
}

.embla__slide_resources:active {
  opacity: 1;
}

.embla__slide:active {
  opacity: 1;
}

.embla__slide_mobile {
  flex: 0 0 100%;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.embla__slide_mobile img {
  width: 100%;
  height: auto;
}

.main_section_mobile {
  display: flex;
  flex-direction: column;
  gap: 25px;
  background-color: colorBlack;
  padding: 20px 16px 31px 16px;
  min-height: 300px;
  flex: 1;

  @media (max-width: breakpoint-sm-450) {
    padding: 16px 12px 24px 12px;
    gap: 20px;
    min-height: 250px;
  }

  @media (max-width: breakpoint-sm-320) {
    padding: 12px 8px 20px 8px;
    gap: 16px;
    min-height: 200px;
  }
}

.main_section_mobile_resources {
  display: flex;
  flex-direction: column;
  gap: 25px;
  background-color: gray300;
  padding-bottom: 31px;
}

.inner_container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 103px;
  min-height: 512px;
}

.inner_container_resources {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 34px;
}

.section_top {
  display: flex;
  flex-direction: column;
  gap: 30px;
  max-width: 737px;
  transform: translateY(10%);
  transition: transform 1s ease-in-out;
  opacity: 0;

  @media screen and (min-width: breakpoint-xl-2000) {
    max-width: 847px;
    gap: 35px;
  }

  @media (max-width: breakpoint-xl-1440) {
    max-width: 650px;
    gap: 25px;
  }

  @media (max-width: breakpoint-lg) {
    max-width: 550px;
    gap: 20px;
  }

  @media (max-width: breakpoint-md) {
    max-width: 100%;
    gap: 18px;
  }

  @media (max-width: breakpoint-sm) {
    gap: 16px;
  }
}

.section_top_resources {
  display: flex;
  flex-direction: column;
  gap: 30px;
  transform: translateY(10%);
  transition: transform 1s ease-in-out;
  opacity: 0;
}

.section_top_resources:hover {
  .carousel_title_resources > h1 {
    cursor: pointer;
    background: linear-gradient(
      to right,
      brandColorOne,
      brandColorTwo,
      brandColorThree,
      brandColorFour,
      brandColorFive
    );
    background-clip: text;
    color: transparent;
  }
}

.section_top_active {
  transform: translateY(0);
  animation: fadeIn 1s forwards;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}

.section_top_mobile {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
  padding: 16px;

  @media (max-width: breakpoint-sm-450) {
    padding: 12px;
    gap: 6px;
  }

  @media (max-width: breakpoint-sm-320) {
    padding: 8px;
    gap: 4px;
  }
}

.carousel_title > h1 {
  color: colorWhite;
  font-size: 48px;
  font-style: normal;
  font-weight: 700;
  line-height: 62px;

  @media (max-width: breakpoint-xl-1440) {
    font-size: 42px;
    line-height: 54px;
  }

  @media (max-width: breakpoint-lg) {
    font-size: 36px;
    line-height: 46px;
  }

  @media (max-width: breakpoint-md) {
    font-size: 32px;
    line-height: 42px;
  }

  @media (max-width: breakpoint-sm) {
    font-size: 28px;
    line-height: 38px;
  }

  @media (max-width: breakpoint-sm-450) {
    font-size: 24px;
    line-height: 32px;
  }
}

.carousel_resources_link {
  text-decoration: none;
}

.carousel_title_resources > h1 {
  color: gray900;
  font-size: 48px;
  font-style: normal;
  font-weight: 700;
  line-height: 62px;
  transition: all 0.3s ease;
}

.carousel_title_resources_mobile > h1 {
  color: #383838;

  text-align: center;
  font-size: 28px;
  font-style: normal;
  font-weight: 600;
  line-height: 38px;
  letter-spacing: -0.84px;
}

.carousel_title_mobile > h1 {
  color: colorWhite;
  text-align: center;
  font-size: 28px;
  font-style: normal;
  font-weight: 600;
  line-height: 38px;
  letter-spacing: -0.84px;

  @media (max-width: breakpoint-sm-450) {
    font-size: 24px;
    line-height: 32px;
    letter-spacing: -0.72px;
  }

  @media (max-width: breakpoint-sm-320) {
    font-size: 20px;
    line-height: 28px;
    letter-spacing: -0.6px;
  }
}

.homepage__desc {
  color: colorWhite;
  font-size: 22px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px;

  @media (max-width: breakpoint-xl-1440) {
    font-size: 20px;
    line-height: 28px;
  }

  @media (max-width: breakpoint-lg) {
    font-size: 18px;
    line-height: 26px;
  }

  @media (max-width: breakpoint-md) {
    font-size: 16px;
    line-height: 24px;
  }

  @media (max-width: breakpoint-sm) {
    font-size: 14px;
    line-height: 22px;
  }
}

.homepage__desc_resources {
  color: #383838;

  font-size: 22px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px;
}

.homepage__desc_resources > p {
  margin: 0;
}

.homepage__desc_mobile {
  color: colorWhite;
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 23px;

  @media (max-width: breakpoint-sm-450) {
    font-size: 14px;
    line-height: 20px;
  }

  @media (max-width: breakpoint-sm-320) {
    font-size: 12px;
    line-height: 18px;
  }
}

.homepage__desc_mobile_resources {
  color: #383838;

  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 23px;
}

.homepage__desc_mobile_resources > p {
  margin: 0;
}

.circular_button_mobile {
  display: flex;
  justify-content: center;
}

.bottom_section_controls {
  display: flex;
  flex-direction: column;
  gap: 18px;
  max-width: 540px;
  z-index: 3;
}

.bottom_section_controls_mobile {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 18px;
}

.service_title {
  color: colorWhite;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.service_title_resources {
  color: #383838;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.service_title_resources_mobile {
  color: #383838;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.embla__controls {
  display: flex;
}

.embla__dots {
  display: flex;
  gap: 5px;
}

.embla__dot {
  width: 100px;
  height: 4px;
  margin: 10px 0;
  border-radius: 10px;
  background-color: #6f6f6f;
}

.embla__dot_mobile {
  width: 50px;
  height: 4px;
  border-radius: 10px;
  background-color: #6f6f6f;
}

.embla__dot_selected {
  width: 100px;
  height: 4px;
  border-radius: 10px;
  position: relative;
}

.embla__dot_selected_mobile {
  width: 50px;
  height: 4px;
  border-radius: 10px;
  position: relative;
}

.embla__dot_selected_mobile::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  border-radius: 10px;
  background: linear-gradient(
    to right,
    #febe10,
    #f47a37,
    #f05443,
    #d91a5f,
    #b41f5e
  );
  animation: progress 6s linear forwards;
}

.embla__dot_selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  border-radius: 10px;
  background: linear-gradient(
    to right,
    #febe10,
    #f47a37,
    #f05443,
    #d91a5f,
    #b41f5e
  );
  animation: progress 6s linear forwards;
}

@keyframes progress {
  100% {
    width: 100%;
  }
}

.circular_button {
  width: fit-content;
}
